/* ===== ADMIN SIDEBAR DESIGN SYSTEM ===== */
.AdminSidebar {
  display: flex;
  flex-direction: column;
  background-color: var(--sidebar-bg);
  width: 100%;
  height: 100%;
  transition: var(--transition-all);
  overflow: hidden;
  position: relative;
  border-right: 1px solid var(--sidebar-border);
}

.AdminSidebar__container {
  display: flex;
  flex-direction: column;
  padding: var(--padding-lg);
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

/* ===== LOGO SECTION ===== */
.AdminSidebar__logo {
  text-align: center;
  padding: var(--padding-lg) 0;
  border-bottom: 1px solid var(--sidebar-border);
  margin-bottom: var(--margin-lg);
}

.AdminSidebar__logo h3 {
  color: var(--primary-color);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin: 0;
  line-height: var(--line-height-tight);
}

.AdminSidebar__logo span {
  color: var(--gray-500);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  display: block;
  margin-top: var(--margin-xs);
}

/* ===== MOBILE PROFILE SECTION ===== */
.AdminSidebar__mobile-profile {
  display: none;
  padding: var(--padding-lg);
  border-bottom: 1px solid var(--sidebar-border);
  margin-bottom: var(--margin-lg);
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
  border-radius: var(--border-radius-lg);
}

.mobile-profile-header {
  display: flex;
  align-items: center;
  gap: var(--gap-md);
  margin-bottom: var(--margin-md);
}

.mobile-profile-header .profile-avatar {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-full);
  overflow: hidden;
  border: 2px solid var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-light);
  color: var(--primary-color);
  font-size: var(--icon-size-lg);
  flex-shrink: 0;
}

.mobile-profile-header .profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.mobile-profile-header .profile-info {
  flex: 1;
  min-width: 0;
}

.mobile-profile-header .profile-info h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin: 0;
  line-height: var(--line-height-tight);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mobile-profile-header .profile-info p {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin: var(--margin-xs) 0;
  line-height: var(--line-height-tight);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mobile-profile-header .profile-role {
  font-size: var(--font-size-xs);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  background: var(--primary-light);
  padding: var(--padding-xs) var(--padding-sm);
  border-radius: var(--border-radius-full);
  display: inline-block;
}

.mobile-profile-actions {
  display: flex;
  gap: var(--gap-sm);
}

.profile-action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--gap-sm);
  padding: var(--padding-sm) var(--padding-md);
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius-md);
  background: var(--white);
  color: var(--gray-700);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-all);
}

.profile-action-btn:hover {
  background: var(--gray-100);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}

.profile-action-btn .action-icon {
  font-size: var(--icon-size-sm);
}

/* ===== NAVIGATION MENU ===== */
.AdminSidebar__menu {
  display: flex;
  flex-direction: column;
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
  gap: var(--gap-xs);
}

.AdminSidebar__item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--padding-md) var(--padding-lg);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: var(--transition-all);
  color: var(--gray-700);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  position: relative;
  border: 1px solid transparent;
}

.AdminSidebar__item .item-content {
  display: flex;
  align-items: center;
  flex: 1;
  gap: var(--gap-md);
}

.AdminSidebar__item:hover {
  background-color: var(--sidebar-item-hover);
  color: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateX(4px);
}

.AdminSidebar__item.active {
  background: linear-gradient(135deg, var(--sidebar-item-active) 0%, var(--primary-hover) 100%);
  color: var(--sidebar-item-active-text);
  font-weight: var(--font-weight-semibold);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

/* ===== DISABLED STATE ===== */
.AdminSidebar__item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  color: var(--gray-400);
}

.AdminSidebar__item.disabled:hover {
  background-color: transparent;
  color: var(--gray-400);
  cursor: not-allowed;
  transform: none;
  border-color: transparent;
}

/* ===== COMING SOON BADGE ===== */
.coming-soon-badge {
  background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-hover) 100%);
  color: var(--white);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  padding: var(--padding-xs) var(--padding-sm);
  border-radius: var(--border-radius-full);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  margin-left: var(--margin-sm);
  white-space: nowrap;
  box-shadow: var(--shadow-sm);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: var(--shadow-sm);
    transform: scale(1);
  }
  50% {
    box-shadow: var(--shadow-md);
    transform: scale(1.02);
  }
  100% {
    box-shadow: var(--shadow-sm);
    transform: scale(1);
  }
}

/* ===== ICON STYLES ===== */
.AdminSidebar__icon {
  font-size: var(--icon-size-md);
  min-width: var(--icon-size-md);
  transition: var(--transition-transform);
  color: inherit;
}

.AdminSidebar__item:hover .AdminSidebar__icon {
  transform: scale(1.1);
}

.AdminSidebar__item.active .AdminSidebar__icon {
  transform: scale(1.15);
}

/* ===== LOGOUT SECTION ===== */
.AdminSidebar__logout {
  margin-top: auto;
  border-top: 1px solid var(--sidebar-border);
  padding-top: var(--padding-lg);
}

.AdminSidebar__logout .logout-item {
  color: var(--error-color);
  font-weight: var(--font-weight-semibold);
  border: 1px solid transparent;
}

.AdminSidebar__logout .logout-item:hover {
  background-color: var(--error-light);
  color: var(--error-hover);
  border-color: var(--error-color);
  transform: translateX(4px);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Desktop and Large Tablet */
@media (min-width: 1025px) {
  .AdminSidebar__mobile-profile {
    display: none;
  }
}

/* Tablet Responsive */
@media (max-width: 1024px) and (min-width: 769px) {
  .AdminSidebar__container {
    padding: var(--padding-md);
  }

  .AdminSidebar__item {
    padding: var(--padding-sm) var(--padding-md);
    font-size: var(--font-size-sm);
  }

  .AdminSidebar__icon {
    font-size: var(--icon-size-sm);
    min-width: var(--icon-size-sm);
  }

  .coming-soon-badge {
    font-size: var(--font-size-xs);
    padding: var(--padding-xs) var(--padding-sm);
    margin-left: var(--margin-xs);
  }

  .AdminSidebar__mobile-profile {
    display: none;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .AdminSidebar {
    background-color: var(--sidebar-bg);
    box-shadow: var(--shadow-xl);
    border-right: none;
  }

  .AdminSidebar__container {
    width: 100%;
    height: 100%;
    background-color: var(--sidebar-bg);
    overflow-y: auto;
    padding: var(--padding-lg);
    -webkit-overflow-scrolling: touch;
  }

  /* Show mobile profile section */
  .AdminSidebar__mobile-profile {
    display: block;
  }

  .AdminSidebar__logo {
    padding: var(--padding-md) 0;
    border-bottom: 1px solid var(--sidebar-border);
    margin-bottom: var(--margin-md);
  }

  .AdminSidebar__logo h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--margin-xs);
  }

  .AdminSidebar__logo span {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
  }

  .AdminSidebar__item {
    padding: var(--padding-lg);
    margin-bottom: var(--margin-xs);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-base);
  }

  .AdminSidebar__item:hover {
    background-color: var(--sidebar-item-hover);
    transform: none;
    border-color: var(--primary-color);
  }

  .AdminSidebar__item.active {
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-lighter) 100%);
    color: var(--primary-color);
    border-left: 4px solid var(--primary-color);
    border-radius: var(--border-radius-lg);
    font-weight: var(--font-weight-semibold);
  }

  .AdminSidebar__item .item-content {
    gap: var(--gap-lg);
  }

  .AdminSidebar__item span {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
  }

  .AdminSidebar__icon {
    font-size: var(--icon-size-lg);
    min-width: var(--icon-size-lg);
  }

  .coming-soon-badge {
    font-size: var(--font-size-xs);
    padding: var(--padding-xs) var(--padding-sm);
    margin-left: var(--margin-xs);
  }

  .AdminSidebar__logout {
    padding-top: var(--padding-xl);
    border-top: 2px solid var(--sidebar-border);
  }

  .AdminSidebar__logout .logout-item {
    padding: var(--padding-lg);
    font-size: var(--font-size-base);
    border-radius: var(--border-radius-lg);
  }
}

/* Small Mobile Responsive */
@media (max-width: 480px) {
  .AdminSidebar__container {
    padding: var(--padding-md);
  }

  .AdminSidebar__mobile-profile {
    padding: var(--padding-md);
    margin-bottom: var(--margin-md);
  }

  .mobile-profile-header .profile-avatar {
    width: 40px;
    height: 40px;
    font-size: var(--icon-size-md);
  }

  .mobile-profile-header .profile-info h4 {
    font-size: var(--font-size-base);
  }

  .mobile-profile-header .profile-info p {
    font-size: var(--font-size-xs);
  }

  .mobile-profile-actions {
    flex-direction: column;
    gap: var(--gap-xs);
  }

  .profile-action-btn {
    padding: var(--padding-sm);
    font-size: var(--font-size-xs);
  }

  .AdminSidebar__logo {
    padding: var(--padding-sm) 0;
    text-align: center;
  }

  .AdminSidebar__logo h3 {
    font-size: var(--font-size-lg);
  }

  .AdminSidebar__logo span {
    font-size: var(--font-size-xs);
  }

  .AdminSidebar__item {
    padding: var(--padding-md) var(--padding-lg);
    margin-bottom: var(--margin-xs);
    font-size: var(--font-size-sm);
  }

  .AdminSidebar__item span {
    font-size: var(--font-size-sm);
  }

  .AdminSidebar__icon {
    font-size: var(--icon-size-md);
    min-width: var(--icon-size-md);
  }

  .coming-soon-badge {
    font-size: 10px;
    padding: 2px var(--padding-xs);
  }
}

/* Collapsible sidebar for desktop */
.AdminSidebar.collapsed {
  width: 80px; /* Slightly wider for better icon visibility */
}

.AdminSidebar.collapsed .AdminSidebar__logo span,
.AdminSidebar.collapsed .AdminSidebar__item span {
  display: none;
}

.AdminSidebar.collapsed .coming-soon-badge {
  display: none;
}

.AdminSidebar.collapsed .AdminSidebar__logo h3 {
  font-size: var(--smallfont);
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

.AdminSidebar.collapsed .AdminSidebar__item {
  justify-content: center;
  padding: var(--basefont) var(--smallfont);
  position: relative;
}

.AdminSidebar.collapsed .AdminSidebar__icon {
  margin-right: 0;
}

/* Tooltip for collapsed sidebar */
.AdminSidebar.collapsed .AdminSidebar__item:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--dark-gray);
  color: var(--white);
  padding: 6px 12px;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  white-space: nowrap;
  z-index: 1200;
  margin-left: 8px;
  opacity: 0;
  animation: fadeIn 0.2s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* Enhanced smooth transitions */
.AdminSidebar__logo span,
.AdminSidebar__item span {
  transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.AdminSidebar.collapsed .AdminSidebar__logo span,
.AdminSidebar.collapsed .AdminSidebar__item span {
  opacity: 0;
}

/* Scrollbar styling for fixed sidebar */
.AdminSidebar__container::-webkit-scrollbar {
  width: 6px;
}

.AdminSidebar__container::-webkit-scrollbar-track {
  background: var(--bg-gray);
  border-radius: 3px;
}

.AdminSidebar__container::-webkit-scrollbar-thumb {
  background: var(--light-gray);
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.AdminSidebar__container::-webkit-scrollbar-thumb:hover {
  background: var(--dark-gray);
}

/* Active state enhancements */
.AdminSidebar__item.active .AdminSidebar__icon {
  transform: scale(1.1);
}

/* Hover effects */
.AdminSidebar__item:not(.active):hover .AdminSidebar__icon {
  transform: scale(1.05);
}

/* Focus states for accessibility */
.AdminSidebar__item:focus {
  outline: 2px solid var(--btn-color);
  outline-offset: 2px;
}

/* ===== ACCESSIBILITY FEATURES ===== */

/* Focus states for keyboard navigation */
.AdminSidebar__item:focus,
.profile-action-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  border-radius: var(--border-radius-md);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .AdminSidebar__item {
    border: 1px solid currentColor;
  }

  .AdminSidebar__item:hover {
    background-color: currentColor;
    color: var(--white);
  }

  .AdminSidebar__item.active {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .AdminSidebar,
  .AdminSidebar__item,
  .AdminSidebar__icon,
  .profile-action-btn,
  .coming-soon-badge {
    transition: none;
    animation: none;
  }

  .AdminSidebar__item:hover,
  .AdminSidebar__item.active,
  .AdminSidebar__logout .logout-item:hover {
    transform: none;
  }

  .AdminSidebar__item:hover .AdminSidebar__icon,
  .AdminSidebar__item.active .AdminSidebar__icon {
    transform: none;
  }
}

/* ===== SCROLLBAR STYLING ===== */
.AdminSidebar__container::-webkit-scrollbar {
  width: 6px;
}

.AdminSidebar__container::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--border-radius-sm);
}

.AdminSidebar__container::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-colors);
}

.AdminSidebar__container::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

/* Mobile scrollbar */
@media (max-width: 768px) {
  .AdminSidebar__container::-webkit-scrollbar {
    width: 4px;
  }

  .AdminSidebar__container::-webkit-scrollbar-track {
    background: var(--gray-100);
  }

  .AdminSidebar__container::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--border-radius-sm);
  }

  .AdminSidebar__container::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
  }
}
