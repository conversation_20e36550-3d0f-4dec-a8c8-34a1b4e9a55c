/* ===== ADMIN TABLE ACTIONS DESIGN SYSTEM ===== */
/* Scoped to admin components only to avoid global conflicts */

.admin-table-actions {
  display: flex;
  gap: var(--gap-sm);
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
}

/* Ensure admin table actions override any existing table-actions styles */
.table-container.admin-table .admin-table-actions {
  display: flex;
  gap: var(--gap-sm);
  align-items: center;
  justify-content: center;
}

.admin-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--icon-size-2xl);
  height: var(--icon-size-2xl);
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
  font-size: var(--icon-size-sm);
  cursor: pointer;
  transition: var(--transition-all);
  position: relative;
  background: transparent;
  padding: 0;
  outline: none;
  color: var(--gray-600);
}

.admin-action-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ===== BUTTON STATES ===== */
.admin-action-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none !important;
  color: var(--gray-400);
}

.admin-action-btn:disabled:hover {
  transform: none !important;
  opacity: 0.4 !important;
  background-color: transparent !important;
  border-color: transparent !important;
}

/* ===== ACTION BUTTON VARIANTS ===== */

/* View Action Button */
.admin-action-view {
  color: var(--info-color);
}

.admin-action-view:hover:not(:disabled) {
  background-color: var(--info-light);
  border-color: var(--info-color);
  color: var(--info-hover);
  transform: scale(1.05);
}

.admin-action-view:active:not(:disabled) {
  transform: scale(0.95);
  background-color: var(--info-color);
  color: var(--white);
}

/* Edit Action Button */
.admin-action-edit {
  color: var(--accent-blue);
}

.admin-action-edit:hover:not(:disabled) {
  background-color: var(--accent-blue-light);
  border-color: var(--accent-blue);
  color: var(--accent-blue-hover);
  transform: scale(1.05);
}

.admin-action-edit:active:not(:disabled) {
  transform: scale(0.95);
  background-color: var(--accent-blue);
  color: var(--white);
}

/* Delete Action Button */
.admin-action-delete {
  color: var(--error-color);
}

.admin-action-delete:hover:not(:disabled) {
  background-color: var(--error-light);
  border-color: var(--error-color);
  color: var(--error-hover);
  transform: scale(1.05);
}

.admin-action-delete:active:not(:disabled) {
  transform: scale(0.95);
  background-color: var(--error-color);
  color: var(--white);
}

/* Approve Action Button */
.admin-action-approve {
  color: var(--success-color);
}

.admin-action-approve:hover:not(:disabled) {
  background-color: var(--success-light);
  border-color: var(--success-color);
  color: var(--success-hover);
  transform: scale(1.05);
}

.admin-action-approve:active:not(:disabled) {
  transform: scale(0.95);
  background-color: var(--success-color);
  color: var(--white);
}

/* Reject Action Button */
.admin-action-reject {
  color: var(--warning-color);
}

.admin-action-reject:hover:not(:disabled) {
  background-color: var(--warning-light);
  border-color: var(--warning-color);
  color: var(--warning-hover);
  transform: scale(1.05);
}

.admin-action-reject:active:not(:disabled) {
  transform: scale(0.95);
  background-color: var(--warning-color);
  color: var(--white);
}

/* ===== SIZE VARIANTS ===== */

/* Extra Small size variant */
.admin-table-actions.xs .admin-action-btn {
  width: var(--icon-size-lg);
  height: var(--icon-size-lg);
  font-size: var(--font-size-xs);
}

/* Small/Compact size variant */
.admin-table-actions.compact .admin-action-btn,
.admin-table-actions.sm .admin-action-btn {
  width: var(--icon-size-xl);
  height: var(--icon-size-xl);
  font-size: var(--font-size-sm);
}

/* Large size variant */
.admin-table-actions.large .admin-action-btn,
.admin-table-actions.lg .admin-action-btn {
  width: 36px;
  height: 36px;
  font-size: var(--icon-size-lg);
}

/* Extra Large size variant */
.admin-table-actions.xl .admin-action-btn {
  width: 40px;
  height: 40px;
  font-size: var(--icon-size-xl);
}

/* ===== THEME VARIANTS ===== */
.admin-table-actions.theme-minimal .admin-action-btn {
  border: none;
  background: none;
}

.admin-table-actions.theme-outlined .admin-action-btn {
  border: 1px solid var(--gray-300);
  background: var(--white);
}

.admin-table-actions.theme-filled .admin-action-btn {
  background: var(--gray-100);
  border: 1px solid var(--gray-200);
}

.admin-table-actions.theme-filled .admin-action-btn:hover:not(:disabled) {
  background: var(--gray-200);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet Responsive */
@media (max-width: 1024px) {
  .admin-table-actions {
    gap: var(--gap-sm);
  }

  .admin-action-btn {
    width: var(--icon-size-xl);
    height: var(--icon-size-xl);
    font-size: var(--font-size-sm);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .admin-table-actions {
    gap: var(--gap-xs);
  }

  .admin-action-btn {
    width: var(--icon-size-lg);
    height: var(--icon-size-lg);
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-sm);
  }

  /* Ensure touch targets are at least 44px */
  .admin-action-btn {
    min-width: 44px;
    min-height: 44px;
    padding: var(--padding-sm);
  }
}

/* Small Mobile Responsive */
@media (max-width: 480px) {
  .admin-table-actions {
    gap: var(--gap-xs);
    flex-wrap: wrap;
  }

  .admin-action-btn {
    width: var(--icon-size-lg);
    height: var(--icon-size-lg);
    font-size: var(--font-size-xs);
    min-width: 40px;
    min-height: 40px;
  }
}

/* ===== LOADING STATES ===== */
.admin-action-btn.loading {
  pointer-events: none;
  opacity: 0.7;
  position: relative;
}

.admin-action-btn.loading::after {
  content: "";
  position: absolute;
  width: var(--icon-size-sm);
  height: var(--icon-size-sm);
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: var(--border-radius-full);
  animation: spin var(--transition-slow) linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* ===== ACCESSIBILITY FEATURES ===== */

/* High contrast mode support */
@media (prefers-contrast: high) {
  .admin-action-btn {
    border: 2px solid currentColor;
    background-color: var(--white);
  }

  .admin-action-btn:hover:not(:disabled) {
    background-color: currentColor;
    color: var(--white);
  }

  .admin-action-btn:focus {
    outline: 3px solid currentColor;
    outline-offset: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .admin-action-btn {
    transition: none;
  }

  .admin-action-btn:hover:not(:disabled) {
    transform: none;
  }

  .admin-action-btn.loading::after {
    animation: none;
    border: 2px solid currentColor;
    border-top: 2px solid transparent;
  }
}

/* Focus visible for keyboard navigation */
.admin-action-btn:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  border-radius: var(--border-radius-md);
}

/* Tooltip support */
.admin-action-btn[data-tooltip] {
  position: relative;
}

.admin-action-btn[data-tooltip]:hover::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--gray-800);
  color: var(--white);
  padding: var(--padding-xs) var(--padding-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  z-index: var(--z-index-tooltip);
  margin-bottom: var(--margin-xs);
  opacity: 0;
  animation: fadeIn var(--transition-fast) ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}
