/* Admin Table Actions Styles */
/* Scoped to admin components only to avoid global conflicts */

.admin-table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
}

/* Ensure admin table actions override any existing table-actions styles */
.table-container.admin-table .admin-table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

.admin-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius, 4px);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background: transparent;
  padding: 0;
  outline: none;
}

.admin-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.admin-action-btn:disabled:hover {
  transform: none !important;
  opacity: 0.5 !important;
}

/* View Action Button */
.admin-action-view {
  background-color: transparent !important;
  color: var(--black);
}

.admin-action-view:hover:not(:disabled) {
  transform: scale(1.05);
  color: var(--btn-color);
}

.admin-action-view:active:not(:disabled) {
  transform: scale(0.95);
}

/* Edit Action Button */
.admin-action-edit:hover {
  background-color: transparent;
  color: var(--second-primary-color);
}

.admin-action-edit:hover:not(:disabled) {
  transform: scale(1.05);
}

.admin-action-edit:active:not(:disabled) {
  transform: scale(0.95);
}

/* Delete Action Button */
.admin-action-delete:hover {
  color: var(--btn-color);
}

.admin-action-delete:hover:not(:disabled) {
  transform: scale(1.05);
}

.admin-action-delete:active:not(:disabled) {
  transform: scale(0.95);
}

/* Alternative color schemes for different admin themes */
.admin-table-actions.theme-green .admin-action-view {
  background-color: #d4edda;
  color: #155724;
}

.admin-table-actions.theme-green .admin-action-view:hover:not(:disabled) {
  background-color: #c3e6cb;
}

.admin-table-actions.theme-purple .admin-action-view {
  background-color: #e2e3ff;
  color: #4c63d2;
}

.admin-table-actions.theme-purple .admin-action-view:hover:not(:disabled) {
  background-color: #d1d3ff;
}

/* Compact size variant */
.admin-table-actions.compact .admin-action-btn {
  width: 28px;
  height: 28px;
  font-size: 12px;
}

/* Large size variant */
.admin-table-actions.large .admin-action-btn {
  width: 36px;
  height: 36px;
  font-size: 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .admin-table-actions {
    gap: 6px;
  }

  .admin-action-btn {
    width: 30px;
    height: 30px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .admin-table-actions {
    gap: 4px;
  }

  .admin-action-btn {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
}

/* Loading state */
.admin-action-btn.loading {
  pointer-events: none;
  opacity: 0.7;
}

.admin-action-btn.loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .admin-action-btn {
    border: 1px solid currentColor;
  }

  .admin-action-btn:hover:not(:disabled) {
    background-color: currentColor;
    color: white;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .admin-action-btn {
    transition: none;
  }

  .admin-action-btn:hover:not(:disabled) {
    transform: none;
  }

  .admin-action-btn.loading::after {
    animation: none;
  }
}
