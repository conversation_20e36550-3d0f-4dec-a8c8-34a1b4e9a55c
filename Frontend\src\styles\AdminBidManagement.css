/* AdminBidManagement Component Styles */
.AdminBidManagement {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Header */
.AdminBidManagement .AdminBidManagement__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--heading6);
}
.AdminUserManagement__main {
  display: flex;

  align-items: center;
  gap: 10px;
  width: 100%;
}
.AdminBidManagement .header-left {
  flex: 1;
  max-width: 400px;
}

.AdminBidManagement .search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.AdminBidManagement .search-icon {
  position: absolute;
  left: var(--smallfont);
  color: var(--dark-gray);
  font-size: var(--basefont);
  z-index: 1;
}

.AdminBidManagement .search-input {
  width: 100%;
  padding: var(--smallfont) var(--smallfont) var(--smallfont) 40px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  background-color: var(--white);
  transition: all 0.3s ease;
  font-size: 12px;
}

.AdminBidManagement .search-input:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
}

.AdminBidManagement .search-input::placeholder {
  color: var(--dark-gray);
}

.AdminBidManagement .header-right {
  display: flex;
  gap: var(--smallfont);
}

.AdminBidManagement .btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.AdminBidManagement .btn-primary {
  background-color: var(--btn-color);
  color: var(--white);
}

.AdminBidManagement .btn-primary:hover {
  background-color: #d63c2a;
}

.AdminBidManagement .btn-success {
  background-color: #10b981;
  color: var(--white);
}

.AdminBidManagement .btn-success:hover {
  background-color: #059669;
}

.AdminBidManagement .btn-warning {
  background-color: #f59e0b;
  color: var(--white);
}

.AdminBidManagement .btn-warning:hover {
  background-color: #d97706;
}

.AdminBidManagement .btn-danger {
  background-color: #ef4444;
  color: var(--white);
}

.AdminBidManagement .btn-danger:hover {
  background-color: #dc2626;
}

.AdminBidManagement .btn-outline {
  background-color: transparent;
  color: var(--secondary-color);
  border: 1px solid var(--light-gray);
}

.AdminBidManagement .btn-outline:hover {
  background-color: var(--bg-gray);
  color: var(--white);
}

.AdminBidManagement .btn-outline:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Filters */
.AdminBidManagement .AdminBidManagement__filters {
  display: flex;
  align-items: center;
  gap: var(--basefont);
}

.AdminBidManagement .filter-group {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.AdminBidManagement .filter-icon {
  color: var(--dark-gray);
  font-size: var(--basefont);
}

.AdminBidManagement .filter-select {
  padding: var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  background-color: var(--white);
  cursor: pointer;
}

.AdminBidManagement .filter-select:focus {
  outline: none;
  border-color: var(--btn-color);
}

.AdminBidManagement .bulk-actions {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  margin-left: auto;
}

.AdminBidManagement .selected-count {
  font-size: var(--smallfont);
  color: var(--secondary-color);
  font-weight: 600;
}

/* Table */
.AdminBidManagement .AdminBidManagement__table {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.AdminBidManagement .table-container {
  overflow-x: auto;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.AdminBidManagement .bids-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--smallfont);
}

.AdminBidManagement .bids-table th {
  background-color: var(--bg-gray);
  padding: var(--basefont);
  text-align: left;
  font-weight: 600;
  color: var(--secondary-color);
  border-bottom: 1px solid var(--light-gray);
  white-space: nowrap;
}

.AdminBidManagement .bids-table td {
  padding: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
  vertical-align: middle;
}

.AdminBidManagement .bids-table tr:hover {
  background-color: var(--bg-gray);
}

/* Bid ID Column */
.AdminBidManagement .bid-id {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.AdminBidManagement .bid-id-text {
  font-weight: 600;
  color: var(--secondary-color);
}

.AdminBidManagement .auto-bid-badge,
.AdminBidManagement .highest-bid-badge {
  font-size: var(--extrasmallfont);
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 600;
  text-transform: uppercase;
  width: fit-content;
}

.AdminBidManagement .auto-bid-badge {
  background-color: #dbeafe;
  color: #1e40af;
}

.AdminBidManagement .highest-bid-badge {
  background-color: #fef3c7;
  color: #92400e;
}

/* Content Info */
.AdminBidManagement .content-info {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.AdminBidManagement .content-thumbnail {
  width: 32px;
  height: 32px;

  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--dark-gray);
  font-size: var(--basefont);
  flex-shrink: 0;
}
.AdminBidManagement .content-thumbnail img {
  width: 32px;
  height: 32px;
  object-fit: cover;
  border-radius: var(--border-radius);
}
.AdminBidManagement .content-details {
  flex: 1;
  min-width: 0;
}

.AdminBidManagement .content-title {
  display: block;
  font-weight: 500;
  color: var(--secondary-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

/* Bidder Info */
.AdminBidManagement .bidder-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.AdminBidManagement .bidder-name {
  font-weight: 500;
  color: var(--secondary-color);
}

.AdminBidManagement .bidder-email {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* Bid Amount */
.AdminBidManagement .bid-amount {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.AdminBidManagement .amount {
  font-weight: 600;
  color: var(--secondary-color);
}

.AdminBidManagement .max-auto-bid {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* Status Badges */
.AdminBidManagement .status-badge {
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: uppercase;
  white-space: nowrap;
}

.AdminBidManagement .status-badge.active {
  background-color: #dcfce7;
  color: #166534;
}

.AdminBidManagement .status-badge.won {
  background-color: #dbeafe;
  color: #1e40af;
}

.AdminBidManagement .status-badge.lost {
  background-color: #fef2f2;
  color: #991b1b;
}

.AdminBidManagement .status-badge.outbid {
  background-color: #fef3c7;
  color: #92400e;
}

.AdminBidManagement .status-badge.cancelled {
  background-color: #f3f4f6;
  color: #6b7280;
}

.AdminBidManagement .status-badge.cancelled {
  background-color: #f3f4f6;
  color: #6b7280;
}

/* Table Actions */
.AdminBidManagement .table-actions {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
}

.AdminBidManagement .btn-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.AdminBidManagement .btn-action.approve {
  background-color: #dcfce7;
  color: #166534;
}

.AdminBidManagement .btn-action.approve:hover {
  background-color: #bbf7d0;
  transform: scale(1.05);
}

.AdminBidManagement .btn-action.reject {
  background-color: #fef3c7;
  color: #92400e;
}

.AdminBidManagement .btn-action.reject:hover {
  background-color: #fde68a;
  transform: scale(1.05);
}

/* Tooltip for action buttons */
.AdminBidManagement .btn-action::before {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--secondary-color);
  color: var(--white);
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: var(--z-index-tooltip);
  pointer-events: none;
}

.AdminBidManagement .btn-action:hover::before {
  opacity: 1;
  visibility: visible;
  bottom: calc(100% + 8px);
}

.AdminBidManagement .btn-action.edit {
  background-color: var(--bg-gray);
  color: var(--secondary-color);
}

.AdminBidManagement .btn-action.edit:hover {
  background-color: var(--light-gray);
}

.AdminBidManagement .btn-action.delete {
  background-color: #fef2f2;
  color: #ef4444;
}

.AdminBidManagement .btn-action.delete:hover {
  background-color: #fee2e2;
}

/* No Results */
.AdminBidManagement .no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading3);
  text-align: center;
}

.AdminBidManagement .no-results-icon {
  font-size: var(--heading2);
  color: var(--light-gray);
  margin-bottom: var(--basefont);
}

.AdminBidManagement .no-results h3 {
  margin: 0 0 var(--smallfont) 0;
  color: var(--secondary-color);
}

.AdminBidManagement .no-results p {
  margin: 0;
  color: var(--dark-gray);
}

/* Loading and Error States */
.AdminBidManagement .loading-row,
.AdminBidManagement .error-row {
  text-align: center;
  padding: 40px 20px;
}

.AdminBidManagement .loading-row {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--basefont);
}

.AdminBidManagement .loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--light-gray);
  border-top: 3px solid var(--btn-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.AdminBidManagement .error-row {
  color: #dc2626;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--basefont);
}

/* Pagination */
.AdminBidManagement .AdminBidManagement__pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--basefont);
  background-color: var(--white);
  border-radius: var(--border-radius);
}

.AdminBidManagement .pagination-info {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.AdminBidManagement .pagination-controls {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
}

.AdminBidManagement .page-number {
  padding: 6px 12px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  color: var(--secondary-color);
  cursor: pointer;
  transition: all 0.3s ease;
}

.AdminBidManagement .page-number.active {
  background-color: var(--btn-color);
  color: var(--white);
}

/* Responsive styles */
@media (max-width: 768px) {
  .AdminBidManagement .AdminBidManagement__header {
    flex-direction: column;
    align-items: stretch;
  }

  .AdminBidManagement .header-left {
    max-width: none;
  }

  .AdminBidManagement .AdminBidManagement__filters {
    flex-wrap: wrap;
  }

  .AdminBidManagement .bulk-actions {
    margin-left: 0;
    margin-top: var(--smallfont);
    width: 100%;
  }

  .AdminBidManagement .bids-table {
    font-size: var(--extrasmallfont);
  }

  .AdminBidManagement .bids-table th,
  .AdminBidManagement .bids-table td {
    padding: var(--smallfont);
  }

  .AdminBidManagement .content-thumbnail {
    width: 32px;
    height: 24px;
    font-size: var(--smallfont);
  }

  .AdminBidManagement .content-title {
    max-width: 120px;
  }

  .AdminBidManagement .AdminBidManagement__pagination {
    flex-direction: column;
    gap: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .AdminBidManagement .table-actions {
    flex-direction: column;
  }

  .AdminBidManagement .btn-action {
    width: 28px;
    height: 28px;
    font-size: var(--extrasmallfont);
  }

  .AdminBidManagement .bid-id-text {
    font-size: var(--extrasmallfont);
  }

  .AdminBidManagement .auto-bid-badge,
  .AdminBidManagement .highest-bid-badge {
    font-size: 8px;
    padding: 1px 4px;
  }
}
