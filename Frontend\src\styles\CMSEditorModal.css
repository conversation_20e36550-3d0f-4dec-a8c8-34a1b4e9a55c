/* CMSEditorModal Component Styles */
.CMSEditorModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1500; /* Above navbar (1000) and sidebar (200) */
  display: flex;
  align-items: center;
  justify-content: center;
}

.CMSEditorModal__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.CMSEditorModal__container {
  position: relative;
  width: 95%;
  max-width: 1200px;
  height: 90vh;
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Header */
.CMSEditorModal__header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white);
  padding: var(--basefont) var(--heading6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  margin-right: var(--basefont);
}

.header-content h2 {
  margin: 0;
  font-size: var(--heading5);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.header-actions {
  display: flex;
  gap: var(--smallfont);
}

.close-btn {
  background: none;
  border: none;
  color: var(--white);
  font-size: var(--heading5);
  cursor: pointer;
  padding: var(--smallfont);
  border-radius: var(--border-radius);
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Content */
.CMSEditorModal__content {
  flex: 1;
  overflow-y: auto;
  padding: var(--heading6);
}

/* Editor Form */
.editor-form {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

.form-section {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--basefont);
}

.form-section h3 {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
  border-bottom: 1px solid var(--light-gray);
  padding-bottom: var(--smallfont);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.form-group label {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
}

.form-input,
.form-textarea,
.form-select {
  padding: var(--smallfont) var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-family: inherit;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.form-input.error,
.form-textarea.error {
  border-color: var(--error-color);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.content-editor {
  min-height: 300px;
  font-family: 'Courier New', monospace;
  line-height: 1.6;
}

.error-message {
  color: var(--error-color);
  font-size: var(--smallfont);
  margin-top: 4px;
}

.char-count {
  color: var(--gray);
  font-size: var(--smallfont);
  text-align: right;
  margin-top: 4px;
}

/* Editor Toolbar */
.editor-toolbar {
  display: flex;
  gap: var(--basefont);
  padding: var(--smallfont);
  background-color: var(--bg-gray);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  margin-bottom: var(--smallfont);
}

.toolbar-group {
  display: flex;
  gap: 4px;
  padding-right: var(--basefont);
  border-right: 1px solid var(--light-gray);
}

.toolbar-group:last-child {
  border-right: none;
  padding-right: 0;
}

.toolbar-group button {
  background: none;
  border: none;
  padding: 6px 8px;
  border-radius: var(--border-radius-small);
  cursor: pointer;
  color: var(--text-color);
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-group button:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

/* Preview Content */
.preview-content {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--heading6);
  background-color: var(--white);
  min-height: 500px;
}

.preview-header {
  margin-bottom: var(--heading6);
  padding-bottom: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
}

.preview-header h1 {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--heading3);
  color: var(--text-color);
}

.featured-image {
  width: 100%;
  max-width: 600px;
  height: auto;
  border-radius: var(--border-radius);
  margin-top: var(--basefont);
}

.preview-body {
  line-height: 1.8;
  color: var(--text-color);
  font-size: var(--basefont);
}

/* Footer */
.CMSEditorModal__footer {
  background-color: var(--bg-gray);
  padding: var(--basefont) var(--heading6);
  border-top: 1px solid var(--light-gray);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-info {
  color: var(--gray);
  font-size: var(--smallfont);
}

.footer-actions {
  display: flex;
  gap: var(--basefont);
}

/* Buttons */
.btn {
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: var(--white);
}

/* Responsive Design */
@media (max-width: 768px) {
  .CMSEditorModal__container {
    width: 98%;
    height: 95vh;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .editor-toolbar {
    flex-wrap: wrap;
  }

  .toolbar-group {
    border-right: none;
    padding-right: 0;
    margin-bottom: var(--smallfont);
  }

  .footer-actions {
    flex-direction: column;
    width: 100%;
  }

  .CMSEditorModal__footer {
    flex-direction: column;
    gap: var(--basefont);
    align-items: stretch;
  }
}

/* Summernote Editor Styles */
.cms-summernote {
  margin-bottom: var(--basefont-sm);
}

.cms-summernote .note-editor {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.cms-summernote .note-editor.note-frame {
  border: 1px solid var(--light-gray);
}

.cms-summernote .note-editor.note-frame:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.cms-summernote .note-toolbar {
  background-color: var(--background-light);
  border-bottom: 1px solid var(--light-gray);
}

.cms-summernote .note-editable {
  background-color: var(--white);
  color: var(--text-color);
  font-size: var(--basefont);
  line-height: 1.6;
  padding: var(--basefont);
}

.cms-summernote .note-editable:focus {
  background-color: var(--white);
}

/* Validation Error Styles */
.validation-error {
  margin-top: var(--basefont-xs);
}

.validation-error .error-message {
  color: var(--error-color);
  font-size: var(--basefont-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--basefont-xs);
}

.validation-error .error-message::before {
  content: "⚠";
  color: var(--error-color);
}

.error-message {
  color: var(--error-color);
  font-size: var(--basefont-sm);
  margin-top: var(--basefont-xs);
  display: block;
}

.char-count {
  color: var(--text-secondary);
  font-size: var(--basefont-sm);
  margin-top: var(--basefont-xs);
  display: block;
}

.meta-description {
  color: var(--text-secondary);
  font-style: italic;
  margin-bottom: var(--basefont);
}

/* Slug field styling */
.slug-toggle-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 0.8rem;
  margin-left: 0.5rem;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
  transition: background-color 0.2s ease;
}

.slug-toggle-btn:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
}

.auto-generated {
  background-color: var(--background-light);
  color: var(--text-secondary);
  cursor: not-allowed;
}

.help-text {
  display: block;
  color: var(--btn-color);
  font-size: var(--basefont-xs);
  margin-top: 0.25rem;
  font-style: italic;
}

.input-with-button {
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-button .form-input {
  flex: 1;
  padding-right: 2.5rem;
}

.reset-slug-btn {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--border-radius);
  transition: color 0.2s ease, background-color 0.2s ease;
}

.reset-slug-btn:hover {
  color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.1);
}
