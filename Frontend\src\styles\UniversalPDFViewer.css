/* Universal PDF Viewer - Simple & Robust */
.universal-pdf-viewer {
  position: relative;
  width: 100%;
  background: var(--white, #ffffff);
  border-radius: var(--border-radius, 8px);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color, #e1e5e9);
  display: flex;
  flex-direction: column;
}

/* PDF iframe */
.universal-pdf-viewer__iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: var(--white, #ffffff);
  
  /* Enhanced mobile compatibility */
  -webkit-overflow-scrolling: touch;
  touch-action: pan-x pan-y zoom;
  
  /* Better rendering */
  display: block;
  flex: 1;
}

/* Loading state */
.universal-pdf-viewer__loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--white, #ffffff);
  z-index: 10;
  gap: 16px;
}

.universal-pdf-viewer__spinner {
  font-size: 2rem;
  color: var(--primary-color, #007bff);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.universal-pdf-viewer__loading p {
  color: var(--text-color, #333333);
  font-size: 1rem;
  margin: 0;
}

/* Error state */
.universal-pdf-viewer--error {
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
}

.universal-pdf-viewer__error-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  height: 100%;
  gap: 1rem;
}

.universal-pdf-viewer__error-icon {
  font-size: 3rem;
  color: var(--error-color, #e53e3e);
  margin-bottom: 0.5rem;
}

.universal-pdf-viewer__error-card h3 {
  margin: 0;
  color: var(--error-color, #e53e3e);
  font-size: 1.25rem;
  font-weight: 600;
}

.universal-pdf-viewer__error-card p {
  margin: 0;
  color: var(--text-color, #333333);
  font-size: 1rem;
}

.universal-pdf-viewer__error-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

/* Mobile native app view */
.universal-pdf-viewer--mobile-native {
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
}

/* Brave browser alternative view */
.universal-pdf-viewer--brave-alternative {
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
}

.universal-pdf-viewer__brave-alternative {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.universal-pdf-viewer__object,
.universal-pdf-viewer__embed {
  flex: 1;
  min-height: 400px;
  /* Hide PDF controls and toolbars */
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.universal-pdf-viewer__final-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.universal-pdf-viewer__fallback-content {
  text-align: center;
  max-width: 400px;
}

.universal-pdf-viewer__warning-icon {
  font-size: 3rem;
  color: #ff8c00;
  margin-bottom: 1rem;
}

.universal-pdf-viewer__help-footer {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.9);
  border-top: 1px solid #ddd;
}

.universal-pdf-viewer__icon--warning {
  color: #ff8c00;
}

.universal-pdf-viewer__help {
  margin-top: 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: var(--border-radius, 8px);
  border: 1px solid #ff8c00;
}

.universal-pdf-viewer__help-details {
  margin: 0;
}

.universal-pdf-viewer__help-details summary {
  font-weight: 500;
  color: #ff8c00;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.universal-pdf-viewer__help-details summary:hover {
  background: rgba(255, 140, 0, 0.1);
}

.universal-pdf-viewer__help-details ol {
  margin: 0.75rem 0 0 0;
  padding-left: 1.5rem;
  color: var(--text-color, #333333);
  line-height: 1.5;
}

.universal-pdf-viewer__help-details li {
  margin-bottom: 0.5rem;
}

.universal-pdf-viewer__mobile-card {
  display: flex;
  flex-direction: column;
  padding: 2rem;
  height: 100%;
  justify-content: center;
}

.universal-pdf-viewer__header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  text-align: left;
}

.universal-pdf-viewer__icon {
  font-size: 4rem;
  color: #dc3545;
  flex-shrink: 0;
}

.universal-pdf-viewer__info h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.5rem;
  color: var(--text-color, #333333);
  font-weight: 600;
}

.universal-pdf-viewer__info p {
  margin: 0 0 0.5rem 0;
  color: var(--dark-gray, #666666);
  font-size: 1rem;
}

.universal-pdf-viewer__note {
  display: inline-flex;
  align-items: center;
  font-size: 0.875rem;
  color: var(--primary-color, #007bff);
  font-weight: 500;
  font-style: italic;
}

.universal-pdf-viewer__actions {
  display: flex;
  gap: 0.75rem;
  flex-direction: column;
}

/* Buttons */
.universal-pdf-viewer__btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius, 8px);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  min-height: 48px; /* Touch-friendly */
  flex: 1;
}

.universal-pdf-viewer__btn--primary {
  background: var(--primary-color, #007bff);
  color: var(--white, #ffffff);
}

.universal-pdf-viewer__btn--primary:hover {
  background: var(--primary-dark, #0056b3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.universal-pdf-viewer__btn--secondary {
  background: var(--btn-color, #6c757d);
  color: var(--white, #ffffff);
}

.universal-pdf-viewer__btn--secondary:hover {
  background: var(--btn-hover-color, #545b62);
  transform: translateY(-1px);
}

/* Helper buttons (floating) */
.universal-pdf-viewer__mobile-helper,
.universal-pdf-viewer__download-helper,
.universal-pdf-viewer__brave-helper {
  position: absolute;
  bottom: 1rem;
  z-index: 5;
}

.universal-pdf-viewer__mobile-helper,
.universal-pdf-viewer__download-helper {
  right: 1rem;
}

.universal-pdf-viewer__brave-helper {
  right: 5rem; /* Next to mobile helper if both present */
}

.universal-pdf-viewer__helper-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--primary-color, #007bff);
  color: var(--white, #ffffff);
  border: none;
  border-radius: 50%;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.universal-pdf-viewer__helper-btn:hover {
  background: var(--primary-dark, #0056b3);
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.universal-pdf-viewer__helper-btn--brave {
  background: #ff8c00;
}

.universal-pdf-viewer__helper-btn--brave:hover {
  background: #ff7700;
}

/* Mobile optimizations */
.universal-pdf-viewer--mobile .universal-pdf-viewer__iframe {
  /* Better mobile PDF rendering */
  -webkit-overflow-scrolling: touch;
  overflow: auto;
  /* Ensure proper touch handling */
  touch-action: pan-x pan-y zoom;
  /* Mobile-specific positioning */
  position: relative;
}

/* Responsive design */
@media (max-width: 768px) {
  .universal-pdf-viewer__mobile-card {
    padding: 1.5rem;
  }

  .universal-pdf-viewer__header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .universal-pdf-viewer__icon {
    font-size: 3rem;
  }

  .universal-pdf-viewer__info h3 {
    font-size: 1.25rem;
  }

  .universal-pdf-viewer__actions {
    gap: 0.5rem;
  }

  .universal-pdf-viewer__btn {
    padding: 0.875rem 1rem;
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .universal-pdf-viewer__mobile-card {
    padding: 1rem;
  }

  .universal-pdf-viewer__icon {
    font-size: 2.5rem;
  }

  .universal-pdf-viewer__info h3 {
    font-size: 1.125rem;
  }

  .universal-pdf-viewer__helper-btn {
    width: 44px;
    height: 44px;
    font-size: 1.1rem;
  }

  .universal-pdf-viewer__mobile-helper,
  .universal-pdf-viewer__download-helper {
    bottom: 0.75rem;
    right: 0.75rem;
  }
}

/* Print styles */
@media print {
  .universal-pdf-viewer__loading,
  .universal-pdf-viewer__mobile-helper,
  .universal-pdf-viewer__download-helper {
    display: none !important;
  }
}

/* Accessibility improvements */
.universal-pdf-viewer__btn:focus,
.universal-pdf-viewer__helper-btn:focus {
  outline: 2px solid var(--primary-color, #007bff);
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .universal-pdf-viewer__spinner,
  .universal-pdf-viewer__btn,
  .universal-pdf-viewer__helper-btn {
    animation: none;
    transition: none;
  }
  
  .universal-pdf-viewer__btn:hover,
  .universal-pdf-viewer__helper-btn:hover {
    transform: none;
  }
}

/* Universal PDF Viewer Styles */
.universal-pdf-viewer {
  display: flex;
  flex-direction: column;
  background: var(--white);
  border-radius: var(--border-radius-medium);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  position: relative;
}

.universal-pdf-viewer__controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 12px;
  width: 100%;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.universal-pdf-viewer__nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background-color: #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.universal-pdf-viewer__nav-btn:hover:not(:disabled) {
  background-color: #e0e0e0;
}

.universal-pdf-viewer__nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.universal-pdf-viewer__page-info {
  font-size: 14px;
  color: #666;
  min-width: 100px;
  text-align: center;
}

.universal-pdf-viewer__document {
  flex: 1;
  width: 100%;
  /* overflow-y: auto; */
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.universal-pdf-viewer__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  min-height: 200px;
}

.universal-pdf-viewer__spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.universal-pdf-viewer__error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  text-align: center;
  color: #666;
}

.universal-pdf-viewer__warning-icon {
  font-size: 48px;
  color: #f39c12;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* React PDF Styles */
.react-pdf__Document {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.react-pdf__Page {

  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: var(--white);
  border-radius: var(--border-radius);
}

.react-pdf__Page__canvas {
  border-radius: var(--border-radius);
}

/* Text Layer Styles */
.react-pdf__Page__textContent {
  border: 1px solid transparent;
  box-shadow: none;
  overflow: hidden;
  opacity: 0.2;
  color: transparent;
  user-select: text;
  cursor: text;
}

.react-pdf__Page__textContent ::selection {
  background-color: var(--btn-color);
  color: transparent;
}

/* Annotation Layer Styles */
.react-pdf__Page__annotations {
  padding: 0;
}

.react-pdf__Page__annotations.annotationLayer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .universal-pdf-viewer__document {
    padding: var(--smallfont);
  }


}

@media (max-width: 480px) {
  .universal-pdf-viewer__document {
    padding: var(--extrasmallfont);
  }
} 