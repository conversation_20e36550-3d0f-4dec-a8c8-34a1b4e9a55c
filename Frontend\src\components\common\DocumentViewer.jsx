import React, { useState, useEffect } from 'react';
import { FaExclamationTriangle } from 'react-icons/fa';
import UniversalPDFViewer from './UniversalPDFViewer';
import '../../styles/DocumentViewer.css';

const DocumentViewer = ({
  fileUrl,
  fileName = '',
  title = 'Document',
  className = '',
  height = '400px',
  showDownload = false,
  onDownload = null
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Initialize component - simplified since all documents are PDFs
  useEffect(() => {
    if (!fileUrl) {
      setHasError(true);
      setIsLoading(false);
      return;
    }

    // Since all documents are PDFs, we can directly proceed to rendering
    setIsLoading(false);
    setHasError(false);
  }, [fileName, fileUrl]);

  // Handle download/external opening
  const handleDownload = () => {
    if (!fileUrl) return;

    // Create a download link
    const link = document.createElement('a');
    link.href = fileUrl;
    link.target = '_blank';
    link.rel = 'noopener noreferrer';

    // Add download attribute for better mobile support
    if (fileName) {
      link.download = fileName;
    }

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className={`document-viewer ${className}`} style={{ height }}>
      {isLoading ? (
        <div className="document-viewer__loading">
          <div className="document-viewer__spinner" />
          <p>Loading PDF document...</p>
        </div>
      ) : hasError ? (
        <div className="document-viewer__error">
          <FaExclamationTriangle className="document-viewer__error-icon" />
          <h3>Error Loading PDF Document</h3>
          <p>Unable to load the PDF preview. Please check the file URL.</p>
          {(showDownload || onDownload) && (
            <button
              className="document-viewer__download-btn"
              onClick={onDownload || handleDownload}
            >
              Download
            </button>
          )}
        </div>
      ) : (
        <UniversalPDFViewer
          fileUrl={fileUrl}
          fileName={fileName}
          title={title}
          className={className}
          height={height}
          showDownload={showDownload}
          onDownload={onDownload}
          showNativeOptions={false}
        />
      )}
    </div>
  );
};

export default DocumentViewer;
