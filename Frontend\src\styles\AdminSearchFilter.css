/* ===== UNIFIED ADMIN SEARCH & FILTER DESIGN SYSTEM ===== */
/* Scoped to admin components only to avoid global conflicts */

/* ===== SEARCH & FILTER CONTAINER ===== */
.admin-search-filter {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--padding-xl);
  margin-bottom: var(--margin-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: var(--transition-all);
}

.admin-search-filter:hover {
  box-shadow: var(--shadow-md);
}

/* ===== SEARCH SECTION ===== */
.admin-search-section {
  margin-bottom: var(--margin-lg);
}

.admin-search-container {
  position: relative;
  display: flex;
  align-items: center;
  max-width: 500px;
  width: 100%;
}

.admin-search-icon {
  position: absolute;
  left: var(--padding-lg);
  color: var(--gray-500);
  font-size: var(--icon-size-sm);
  z-index: 2;
  transition: var(--transition-colors);
}

.admin-search-input {
  width: 100%;
  padding: var(--input-padding);
  padding-left: calc(var(--padding-lg) + var(--icon-size-sm) + var(--gap-sm));
  border: 1px solid var(--input-border);
  border-radius: var(--input-border-radius);
  font-size: var(--input-font-size);
  font-weight: var(--font-weight-normal);
  background-color: var(--input-bg);
  color: var(--input-text);
  transition: var(--transition-all);
  line-height: var(--line-height-normal);
}

.admin-search-input:focus {
  outline: none;
  border-color: var(--input-border-focus);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  background-color: var(--white);
}

.admin-search-input:focus + .admin-search-icon {
  color: var(--primary-color);
}

.admin-search-input::placeholder {
  color: var(--input-placeholder);
  font-weight: var(--font-weight-normal);
}

/* ===== FILTER SECTION ===== */
.admin-filter-section {
  display: flex;
  gap: var(--gap-lg);
  flex-wrap: wrap;
  align-items: flex-end;
}

.admin-filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--gap-sm);
  min-width: 150px;
}

.admin-filter-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-700);
  margin-bottom: var(--margin-xs);
}

.admin-filter-select {
  padding: var(--padding-md) var(--padding-lg);
  border: 1px solid var(--input-border);
  border-radius: var(--input-border-radius);
  font-size: var(--input-font-size);
  font-weight: var(--font-weight-normal);
  background-color: var(--input-bg);
  color: var(--input-text);
  cursor: pointer;
  transition: var(--transition-all);
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right var(--padding-md) center;
  background-size: var(--icon-size-sm);
  padding-right: calc(var(--padding-lg) + var(--icon-size-sm) + var(--gap-sm));
}

.admin-filter-select:focus {
  outline: none;
  border-color: var(--input-border-focus);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  background-color: var(--white);
}

.admin-filter-select:hover {
  border-color: var(--gray-400);
}

/* ===== FILTER ACTIONS ===== */
.admin-filter-actions {
  display: flex;
  gap: var(--gap-md);
  align-items: center;
  margin-left: auto;
}

.admin-filter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--gap-sm);
  padding: var(--btn-padding-sm);
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius-md);
  background: var(--white);
  color: var(--gray-700);
  font-size: var(--btn-font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-all);
  min-width: 100px;
}

.admin-filter-btn:hover {
  background: var(--gray-100);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}

.admin-filter-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.admin-filter-btn.primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: var(--white);
  border-color: var(--primary-color);
}

.admin-filter-btn.primary:hover {
  background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-active) 100%);
  color: var(--white);
}

.admin-filter-btn.secondary {
  background: var(--gray-100);
  color: var(--gray-600);
  border-color: var(--gray-300);
}

.admin-filter-btn.secondary:hover {
  background: var(--gray-200);
  color: var(--gray-800);
}

/* ===== BULK ACTIONS SECTION ===== */
.admin-bulk-actions {
  display: flex;
  align-items: center;
  gap: var(--gap-lg);
  padding: var(--padding-lg);
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--gray-200);
  margin-bottom: var(--margin-lg);
}

.admin-selected-count {
  font-size: var(--font-size-sm);
  color: var(--gray-700);
  font-weight: var(--font-weight-semibold);
  padding: var(--padding-sm) var(--padding-md);
  background: var(--white);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--gray-200);
  min-width: 120px;
  text-align: center;
}

.admin-bulk-action-buttons {
  display: flex;
  gap: var(--gap-sm);
  margin-left: auto;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet Responsive */
@media (max-width: 1024px) {
  .admin-search-filter {
    padding: var(--padding-lg);
  }

  .admin-filter-section {
    gap: var(--gap-md);
  }

  .admin-filter-group {
    min-width: 120px;
  }

  .admin-filter-actions {
    margin-left: 0;
    margin-top: var(--margin-md);
    width: 100%;
    justify-content: flex-start;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .admin-search-filter {
    padding: var(--padding-md);
    margin-bottom: var(--margin-lg);
  }

  .admin-search-section {
    margin-bottom: var(--margin-md);
  }

  .admin-search-container {
    max-width: 100%;
  }

  .admin-filter-section {
    flex-direction: column;
    gap: var(--gap-md);
    align-items: stretch;
  }

  .admin-filter-group {
    min-width: 100%;
  }

  .admin-filter-actions {
    flex-direction: column;
    gap: var(--gap-sm);
    margin-top: var(--margin-md);
    width: 100%;
  }

  .admin-filter-btn {
    width: 100%;
    justify-content: center;
  }

  .admin-bulk-actions {
    flex-direction: column;
    gap: var(--gap-md);
    align-items: stretch;
    padding: var(--padding-md);
  }

  .admin-selected-count {
    min-width: 100%;
    text-align: center;
  }

  .admin-bulk-action-buttons {
    margin-left: 0;
    flex-direction: column;
    gap: var(--gap-sm);
  }
}

/* Small Mobile Responsive */
@media (max-width: 480px) {
  .admin-search-filter {
    padding: var(--padding-sm);
  }

  .admin-search-input,
  .admin-filter-select {
    font-size: var(--font-size-sm);
    padding: var(--padding-sm) var(--padding-md);
  }

  .admin-search-input {
    padding-left: calc(var(--padding-md) + var(--icon-size-sm) + var(--gap-xs));
  }

  .admin-filter-select {
    padding-right: calc(var(--padding-md) + var(--icon-size-sm) + var(--gap-xs));
  }

  .admin-filter-btn {
    padding: var(--padding-sm);
    font-size: var(--font-size-xs);
    min-width: 80px;
  }
}
